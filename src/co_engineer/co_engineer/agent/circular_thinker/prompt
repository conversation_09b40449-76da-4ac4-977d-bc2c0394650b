uv run cli.py --workspace /home/<USER>/POC6 --problem-statement '
# Goal
Your primary objectives are to:
1.  Fetch task details from a specified GitHub Issue URL.
2.  Analyze the issue content to understand the specific work required.
3.  Based on the task type (e.g., bug fix, feature development, documentation, question answering), formulate an initial action plan or outline a solution.

# Input
- `github_issue_url`: A URL pointing to a specific GitHub Issue. Example: "https://eos2git.cec.lab.emc.com/wux13/POC6/issues/3"

# Workflow

1.  **Fetch Issue Details**:
    * You **MUST** use the GitHub command-line tool (`gh`) to view the content of the issue at the provided `github_issue_url`.
    * Example command: `gh issue view [github_issue_url]`
    * Extract key information from the command output, such as:
        * Issue Title
        * Issue Body/Description
        * Labels (if any)
        * Reporter/Author
        * Relevant comments (if concise and important)

2.  **Analyze Task**:
    * Carefully read the retrieved issue title and description.
    * Identify the core problem, request, or question stated in the issue.
    * Determine the primary objective or desired outcome of the issue.

3.  **Formulate Plan or Provide Solution Outline**:
    * **If the issue is a Bug Report**:
        * Summarize the bug.
        * Suggest initial diagnostic steps.
        * (If the description is detailed enough) Outline a potential approach to fix it.
    * **If the issue is a Feature Request**:
        * Summarize the requested feature.
        * Break down the feature into smaller, actionable sub-tasks or components.
        * Identify any potential challenges or considerations.
    * **If the issue is a Question or Documentation Task**:
        * Provide a direct answer if possible.
        * Outline the steps to create or update the necessary documentation.
        * Point to existing resources if relevant.
    * **For other types of Issues**: Clearly state your understanding of the task and propose logical next steps or a plan.


# Important Considerations
* If you encounter errors while executing the `gh` command (e.g., permission issues, invalid URL), report the error clearly.
* Your output should be precise and actionable.

# Execute Now
Please process the following GitHub Issue:
`github_issue_url`: "https://eos2git.cec.lab.emc.com/wux13/POC6/issues/3"
'